# Task Management System - Continuation Guide

## Quick Start

To continue working on this project:

1. Read the current context: `.project-tasks/status/quick-context.md`

2. Review the latest session: `.project-tasks/status/session-history/[latest-session].md`

3. Check in-progress tasks and continue implementation

## System Overview

This project uses a custom task management system designed for AI-assisted development. All project tasks, documentation, and progress tracking are organized under the `.project-tasks/` directory.

## Directory Structure

```

.project-tasks/

├── config/

│ ├── project.json # Project configuration and metadata

│ └── settings.json # Task management settings

├── requirements/

│ ├── prd/ # Product requirement documents

│ │ └── [feature]_prd.md

│ └── research/ # Research findings and notes

├── tasks/

│ ├── epic-[name]/ # Epic/feature level organization

│ │ ├── task-[id]/ # Individual task folders

│ │ │ ├── definition.json

│ │ │ ├── implementation.md

│ │ │ ├── tests/

│ │ │ └── artifacts/

│ │ └── epic-meta.json

│ └── standalone/ # Tasks not part of an epic

├── status/

│ ├── current-session.json # Current session state

│ ├── implementation-log.md # Detailed implementation history

│ ├── quick-context.md # Quick reference for continuation

│ └── session-history/ # Previous session summaries

└── reports/

├── progress/ # Progress reports

└── complexity/ # Task complexity analysis

```

## Understanding the Current State

### 1. Quick Context

Start by reading `.project-tasks/status/quick-context.md`. This file contains:

- Current working task

- Immediate next actions

- Key files to review

- Any blocking issues

### 2. Session History

Review the latest session summary in `.project-tasks/status/session-history/`. These files contain:

- Tasks completed in previous sessions

- Decisions made and their rationale

- Code patterns established

- Environment changes

- Detailed next steps

### 3. Current Task Status

Find in-progress tasks:

```bash

find .project-tasks/tasks -name "definition.json" -exec grep -l '"status": "in_progress"' {} \;

```

Then read the task definition to understand:

- Task objectives and requirements

- Dependencies

- Implementation approach

- Acceptance criteria

## Continuing Implementation

### Step 1: Load Context

1. **Read the quick context:**

```bash

cat .project-tasks/status/quick-context.md

```

2. **Review the current task:**

```bash

# Find in-progress task

find .project-tasks/tasks -name "definition.json" -exec grep -l '"status": "in_progress"' {} \; | head -1

```

3. **Check implementation log for current task:**

Look in the task folder for `implementation.md` to see what's been done.

### Step 2: Resume Work

1. **Update session tracking:**

Create/update `.project-tasks/status/current-session.json`:

```json

{

"session_id": "[NEW-UUID]",

"started": "[CURRENT-TIMESTAMP]",

"current_task": "[TASK-ID]",

"tasks_completed": [],

"context_summary": "Resuming from previous session",

"active_files": [],

"pending_decisions": []

}

```

2. **Continue implementation** based on the task's implementation.md file

- Review what's been completed

- Check any noted issues or blockers

- Continue from where the previous session left off

3. **Update implementation log** as you work:

Add to the task's `implementation.md`:

```markdown

## Continuation - [Timestamp]

### Work Completed

- [Describe what you implemented]

### Code Changes

#### File: [filename]

```[language]

// Code changes

```

### Testing Results

[Test outcomes]

```

### Step 3: Task Completion

When completing a task:

1. **Update task definition:**

```json

{

"status": "completed",

"completed": "[TIMESTAMP]"

}

```

2. **Update epic metadata** (if applicable):

Increment `completed_tasks` in the epic's `epic-meta.json`

3. **Add to session log:**

Update `tasks_completed` in `current-session.json`

4. **Document in implementation log:**

Add final summary to the task's `implementation.md`

### Step 4: Select Next Task

To find the next task:

1. **Check task dependencies:**

- Tasks can only start if all dependencies are completed

- Review `definition.json` for each pending task

2. **Consider priority:**

- High priority tasks first

- Within same priority, consider complexity (simpler first for foundation)

3. **Maintain epic coherence:**

- Try to complete tasks within the same epic before moving to another

## Working with Tasks

### Task Definition Structure

Each task's `definition.json` contains:

```json

{

"id": "TASK-XXX",

"epic": "epic-name",

"title": "Task title",

"description": "What needs to be done",

"status": "pending|in_progress|completed|blocked",

"priority": "high|medium|low",

"complexity": 1-10,

"dependencies": ["TASK-XXX", "TASK-YYY"],

"implementation_details": {

"approach": "How to implement",

"key_files": ["files to create/modify"],

"test_strategy": "How to verify"

},

"acceptance_criteria": ["List of criteria"]

}

```

### Implementation Tracking

Each task folder contains:

- `definition.json` - Task metadata and requirements

- `implementation.md` - Detailed log of implementation work

- `tests/` - Test files related to this task

- `artifacts/` - Any generated files or documentation

## Session Management

### Starting a Session

1. Create new session in `current-session.json`

2. Review quick context and previous session

3. Continue from saved state

### During Implementation

1. Keep `implementation.md` updated for current task

2. Document all decisions and code changes

3. Note any blockers or issues

4. Update task status as appropriate

### Before Ending Session

1. **Create session summary** in `.project-tasks/status/session-history/session-[timestamp].md`

2. **Update quick context** in `.project-tasks/status/quick-context.md`

3. **Update implementation log** with overall progress

4. **Document any pending decisions** or blockers

## Important Commands and Checks

### Find all pending tasks:

```bash

find .project-tasks/tasks -name "definition.json" -exec grep -l '"status": "pending"' {} \;

```

### Check task dependencies:

```bash

jq '.dependencies' .project-tasks/tasks/*/task-*/definition.json

```

### View project progress:

```bash

cat .project-tasks/status/implementation-log.md

```

### List all epics:

```bash

ls -la .project-tasks/tasks/epic-*/epic-meta.json

```

## Best Practices

1. **Always read context first** - Don't assume, verify the current state

2. **Update continuously** - Don't wait until the end to update logs

3. **Document decisions** - Explain why, not just what

4. **Test before completing** - Verify acceptance criteria

5. **Preserve context** - Future sessions depend on good documentation

6. **Check dependencies** - Never work on blocked tasks

7. **Follow established patterns** - Review previous implementations

## Common Scenarios

### Resuming an In-Progress Task

1. Find the task in `.project-tasks/tasks/`

2. Read its `implementation.md`

3. Check what's been done vs. acceptance criteria

4. Continue implementation

5. Update logs as you work

### Starting a New Task

1. Find highest priority task with satisfied dependencies

2. Update status to "in_progress"

3. Create initial implementation log entry

4. Begin implementation following the approach in definition

5. Track progress continuously

### Handling Blockers

1. Update task status to "blocked"

2. Document the blocker in implementation.md

3. Add to pending_decisions in current session

4. Find alternative task to work on

5. Create note in quick-context.md

### Session Handoff

1. Complete current atomic work unit

2. Update all logs and status files

3. Create comprehensive session summary

4. Update quick context for next session

5. Ensure all code is committed/saved

## Need More Context?

If you need additional information:

1. Check `.project-tasks/requirements/prd/` for original requirements

2. Review `.project-tasks/requirements/research/` for codebase analysis

3. Look at completed tasks for patterns and examples

4. Check `.project-tasks/reports/` for project analytics

---

**Remember**: This system is designed for continuity. Every action you take should be documented so the next session (whether yours or another agent's) can pick up seamlessly where you left off.