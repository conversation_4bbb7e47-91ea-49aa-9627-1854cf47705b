"use client";

import { useState, useCallback, useEffect, useRef } from 'react';
import { ConversationState, Message, ConversationSession, UserContext } from '../types/conversation';

const initialSession: ConversationSession = {
  generationStatus: 'idle',
  generatedImages: [],
  uploadedFiles: []
};

const initialState: ConversationState = {
  messages: [],
  currentSession: initialSession,
  isTyping: false,
  isConnected: true
};

export function useConversation() {
  const [state, setState] = useState<ConversationState>(initialState);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new messages arrive
  const scrollToBottom = useCallback(() => {
    // Use a small delay to ensure DOM updates are complete
    setTimeout(() => {
      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    }, 50);
  }, []);

  useEffect(() => {
    // Only auto-scroll if we have messages
    if (state.messages.length > 0) {
      scrollToBottom();
    }
  }, [state.messages, scrollToBottom]);

  // Add a new message to the conversation
  const addMessage = useCallback((message: Omit<Message, 'id' | 'timestamp'>) => {
    const newMessage: Message = {
      ...message,
      id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date()
    };

    setState(prev => ({
      ...prev,
      messages: [...prev.messages, newMessage]
    }));

    return newMessage.id;
  }, []);

  // Update a specific message (useful for loading states)
  const updateMessage = useCallback((messageId: string, updates: Partial<Message>) => {
    setState(prev => ({
      ...prev,
      messages: prev.messages.map(msg => 
        msg.id === messageId ? { ...msg, ...updates } : msg
      )
    }));
  }, []);

  // Set typing indicator
  const setTyping = useCallback((isTyping: boolean) => {
    setState(prev => ({ ...prev, isTyping }));
  }, []);

  // Update session data
  const updateSession = useCallback((updates: Partial<ConversationSession>) => {
    setState(prev => ({
      ...prev,
      currentSession: { ...prev.currentSession, ...updates }
    }));
  }, []);

  // Set user context
  const setUserContext = useCallback((userContext: UserContext) => {
    setState(prev => ({ ...prev, userContext }));
  }, []);

  // Add uploaded files to session
  const addUploadedFiles = useCallback((files: File[]) => {
    setState(prev => ({
      ...prev,
      currentSession: {
        ...prev.currentSession,
        uploadedFiles: [...prev.currentSession.uploadedFiles, ...files]
      }
    }));
  }, []);

  // Clear uploaded files
  const clearUploadedFiles = useCallback(() => {
    setState(prev => ({
      ...prev,
      currentSession: {
        ...prev.currentSession,
        uploadedFiles: []
      }
    }));
  }, []);

  // Set product image in session
  const setProductImage = useCallback((imageUrl: string) => {
    setState(prev => ({
      ...prev,
      currentSession: {
        ...prev.currentSession,
        productImage: imageUrl
      }
    }));
  }, []);

  // Add generated images to session
  const addGeneratedImages = useCallback((images: any[]) => {
    setState(prev => ({
      ...prev,
      currentSession: {
        ...prev.currentSession,
        generatedImages: [...prev.currentSession.generatedImages, ...images],
        generationStatus: 'complete'
      }
    }));
  }, []);

  // Clear conversation (reset to initial state)
  const clearConversation = useCallback(() => {
    setState(initialState);
  }, []);

  // Get conversation history for API calls
  const getConversationHistory = useCallback(() => {
    return state.messages.map(msg => ({
      role: msg.type === 'user' ? 'user' : 'assistant',
      content: msg.content
    }));
  }, [state.messages]);

  return {
    // State
    messages: state.messages,
    currentSession: state.currentSession,
    userContext: state.userContext,
    isTyping: state.isTyping,
    isConnected: state.isConnected,
    
    // Actions
    addMessage,
    updateMessage,
    setTyping,
    updateSession,
    setUserContext,
    addUploadedFiles,
    clearUploadedFiles,
    setProductImage,
    addGeneratedImages,
    clearConversation,
    getConversationHistory,
    
    // Refs
    messagesEndRef
  };
}
