import Sidebar from './components/Sidebar';
import MobileHeader from './components/MobileHeader';
import MobileNavigation from './components/MobileNavigation';
import ResponsiveContainer from './components/ResponsiveContainer';
import { MobileNavigationProvider } from '@/contexts/MobileNavigationContext';
import { SidebarProvider } from '@/contexts/SidebarContext';
import { auth } from '@clerk/nextjs/server';
import { redirect } from 'next/navigation';
import ViewportHeightManager from './components/ViewportHeightManager';

export default async function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { userId } = await auth();
  
  if (!userId) {
    redirect('/sign-in');
  }

  return (
    <SidebarProvider>
      <MobileNavigationProvider>
        <ViewportHeightManager />
        <div className="h-screen bg-gray-50 text-black">
          {/* Desktop Sidebar - Hidden on mobile */}
          <div className="hidden lg:block">
            <Sidebar />
          </div>
          
          {/* Mobile Navigation Overlay */}
          <MobileNavigation />
          
          {/* Mobile Header - Hidden on desktop */}
          <MobileHeader />
          
          {/* Main Content - Responsive layout with proper sidebar offset */}
          <main className="h-screen">
            {/* Mobile: Full height minus header, Desktop: Responsive container */}
            <div className="h-full pt-14 lg:pt-0 lg:hidden">
              {children}
            </div>
            
            {/* Desktop: Responsive container with centered content */}
            <div className="hidden lg:block h-full">
              <ResponsiveContainer>
                <div className="h-full py-2">
                  {children}
                </div>
              </ResponsiveContainer>
            </div>
          </main>
        </div>
      </MobileNavigationProvider>
    </SidebarProvider>
  );
}