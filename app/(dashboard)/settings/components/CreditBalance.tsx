// File: app/(dashboard)/settings/components/CreditBalance.tsx

import { useEffect, useState, useCallback } from 'react';
import { Coins } from 'lucide-react';
import { useResponsive } from '@/hooks/useResponsive';

export default function CreditBalance() {
  const [credits, setCredits] = useState<number | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Use a callback to make this function reusable
  const fetchCredits = useCallback(async () => {
    try {
      setLoading(true);
      console.log('[CreditBalance] Fetching credit balance...');
      const res = await fetch('/api/credits');
      
      if (!res.ok) {
        throw new Error('Failed to fetch credits');
      }
      
      const data = await res.json();
      console.log('[CreditBalance] Credit balance:', data.credits);
      setCredits(data.credits);
    } catch (err) {
      console.error('[CreditBalance] Error fetching credits:', err);
      setError('Could not load your credit balance');
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchCredits();

    // Refresh credits every minute
    const interval = setInterval(fetchCredits, 60000);
    
    // Add event listener for credit updates
    const handleCreditUpdate = () => {
      console.log('[CreditBalance] Credit update event received, refreshing...');
      fetchCredits();
    };
    
    window.addEventListener('creditUpdated', handleCreditUpdate);
    
    return () => {
      clearInterval(interval);
      window.removeEventListener('creditUpdated', handleCreditUpdate);
    };
  }, [fetchCredits]);

  const { isMobile } = useResponsive();

  return (
    <div className={`border rounded-lg ${isMobile ? 'p-4' : 'p-6'} bg-white`}>
      <h3 className={`${isMobile ? 'text-base' : 'text-lg'} font-medium ${isMobile ? 'mb-3' : 'mb-4'}`}>Your Credit Balance</h3>
      
      {error ? (
        <div className={`text-red-500 ${isMobile ? 'text-sm' : ''}`}>{error}</div>
      ) : loading ? (
        <div className="animate-pulse flex items-center space-x-2">
          <div className={`${isMobile ? 'h-8 w-8' : 'h-10 w-10'} bg-gray-200 rounded-full`}></div>
          <div className={`${isMobile ? 'h-5' : 'h-6'} w-20 bg-gray-200 rounded`}></div>
        </div>
      ) : (
        <div className={`flex items-center ${isMobile ? 'space-x-2' : 'space-x-3'}`}>
          <div className={`${isMobile ? 'p-2' : 'p-3'} bg-amber-50 rounded-full`}>
            <Coins size={isMobile ? 20 : 24} className="text-amber-500" />
          </div>
          <div>
            <div className={`${isMobile ? 'text-xl' : 'text-2xl'} font-bold`}>{credits}</div>
            <div className={`${isMobile ? 'text-xs' : 'text-sm'} text-gray-500`}>Available credits</div>
          </div>
        </div>
      )}
      
      <div className={`${isMobile ? 'mt-3' : 'mt-4'} ${isMobile ? 'text-xs' : 'text-sm'} text-gray-600`}>
        <p>1 credit = 1 image</p>
      </div>
    </div>
  );
}