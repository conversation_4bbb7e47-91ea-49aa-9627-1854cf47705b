"use client";

import React, { useState, useEffect, useCallback } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { UserButton, useUser } from '@clerk/nextjs';
import {
  ChevronLeft,
  ChevronRight,
  Plus,
  Library,
  Settings,
  Coins
} from 'lucide-react';
import { useSidebar } from '@/hooks/useSidebar';

export default function Sidebar() {
  const { isCollapsed, toggleCollapsed } = useSidebar();
  const [credits, setCredits] = useState<number | null>(null);
  const [loading, setLoading] = useState(true);
  const pathname = usePathname();
  const { user } = useUser();
  
  const isActive = (path: string) => {
    return pathname === path || pathname.startsWith(path + '/');
  };

  // Create a fetchCredits callback
  const fetchCredits = useCallback(async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/credits');
      if (response.ok) {
        const data = await response.json();
        setCredits(data.credits);
      }
    } catch (error) {
      console.error('Error fetching credits:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchCredits();

    const handleCreditUpdate = () => {
      fetchCredits();
    };
    
    window.addEventListener('creditUpdated', handleCreditUpdate);
    
    return () => {
      window.removeEventListener('creditUpdated', handleCreditUpdate);
    };
  }, [fetchCredits]);

  const navItems = [
    { 
      label: 'Create', 
      href: '/create', 
      icon: <Plus size={20} /> 
    },
    { 
      label: 'Library', 
      href: '/library', 
      icon: <Library size={20} /> 
    },
    { 
      label: 'Settings', 
      href: '/settings', 
      icon: <Settings size={20} /> 
    },
  ];

  return (
    <>
      <div className={`
        fixed left-0 top-0 h-full bg-white border-r border-gray-200 z-50 transition-all duration-300 ease-in-out
        ${isCollapsed ? 'w-16' : 'w-64'}
      `}>
        <div className="flex flex-col h-full">
        {/* Header with Logo */}
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex-1 overflow-hidden">
              <h1 
                className={`text-xl font-bold transition-all duration-300 ease-in-out whitespace-nowrap ${
                  isCollapsed 
                    ? 'opacity-0 -translate-x-4' 
                    : 'opacity-100 translate-x-0'
                }`}
                style={{ color: '#FD2D55' }}
              >
                MARKET-ME
              </h1>
            </div>
            <button
              onClick={toggleCollapsed}
              className="p-1.5 rounded-lg hover:bg-gray-100 transition-colors flex-shrink-0"
            >
              {isCollapsed ? (
                <ChevronRight size={16} className="text-gray-600" />
              ) : (
                <ChevronLeft size={16} className="text-gray-600" />
              )}
            </button>
          </div>
        </div>

        {/* Navigation */}
        <div className="flex-1 py-4">
          <nav className="space-y-2 px-3">
            {navItems.map((item) => (
              <Link
                key={item.href}
                href={item.href}
                className={`
                  flex items-center gap-3 px-3 py-2.5 rounded-lg transition-all text-sm font-medium
                  ${isActive(item.href) 
                    ? 'bg-[#FD2D55] text-white' 
                    : 'text-gray-700 hover:bg-gray-100'
                  }
                  ${isCollapsed ? 'justify-center' : ''}
                `}
                title={isCollapsed ? item.label : undefined}
              >
                <span className="flex-shrink-0">{item.icon}</span>
                {!isCollapsed && <span>{item.label}</span>}
              </Link>
            ))}
          </nav>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-gray-200 p-3 space-y-3">
          {/* Credits Display */}
          <Link 
            href="/settings" 
            className={`
              flex items-center gap-3 px-3 py-2 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors
              ${isCollapsed ? 'justify-center' : ''}
            `}
            title={isCollapsed ? `${credits} Credits` : undefined}
          >
            <Coins size={18} className="text-amber-500 flex-shrink-0" />
            {!isCollapsed && (
              <div className="flex items-center gap-2">
                {loading ? (
                  <span className="w-5 h-5 bg-gray-200 rounded-full animate-pulse"></span>
                ) : (
                  <span className="font-medium">{credits}</span>
                )}
                <span className="text-sm text-gray-500">Credits</span>
              </div>
            )}
          </Link>
          
          {/* User Profile */}
          <div className={`
            flex items-center gap-3 px-3 py-2
            ${isCollapsed ? 'justify-center' : ''}
          `}>
            <UserButton 
              afterSignOutUrl="/" 
              appearance={{
                elements: {
                  avatarBox: "w-8 h-8",
                }
              }}
            />
            {!isCollapsed && (
              <div className="flex-1 min-w-0">
                <div className="text-sm font-medium text-gray-900 truncate">
                  {user?.firstName && user?.lastName 
                    ? `${user.firstName} ${user.lastName}`
                    : user?.fullName || user?.firstName || 'User'
                  }
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
      </div>
    </>
  );
}
