// app/(dashboard)/components/Header.tsx
"use client";

import React, { useEffect, useState, useCallback } from 'react';
import { UserButton } from '@clerk/nextjs';
import Link from 'next/link';
import { Coins } from 'lucide-react';

export default function Header() {
  const [credits, setCredits] = useState<number | null>(null);
  const [loading, setLoading] = useState(true);

  // Create a fetchCredits callback that can be reused and referenced in the effect
  const fetchCredits = useCallback(async () => {
    try {
      setLoading(true);
      console.log('[Header] Fetching credits...');
      const response = await fetch('/api/credits');
      if (response.ok) {
        const data = await response.json();
        console.log('[Header] Credits fetched:', data.credits);
        setCredits(data.credits);
      }
    } catch (error) {
      console.error('[Header] Error fetching credits:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    // Fetch credits on mount
    fetchCredits();

    // Set up event listener for credit updates
    const handleCreditUpdate = () => {
      console.log('[Header] Credit update event received, refreshing...');
      fetchCredits();
    };
    
    // Add event listener for the creditUpdated event
    window.addEventListener('creditUpdated', handleCreditUpdate);
    
    // Clean up the event listener when the component unmounts
    return () => {
      window.removeEventListener('creditUpdated', handleCreditUpdate);
    };
  }, [fetchCredits]); // Include fetchCredits in the dependencies

  return (
    <header className="sticky top-0 z-10 border-b border-gray-200 bg-white px-4 py-3 md:py-4">
      <div className="flex w-full items-center justify-between">
        <div className="flex flex-col items-start pl-0">
          <h1 className="text-2xl font-bold md:text-3xl" style={{ color: '#FD2D55' }}>MARKET-ME</h1>
          <p className="hidden text-sm text-gray-600 md:block">
            Scale Your Style: 1000x More Content, 1000x Less Cost
          </p>
        </div>
        <div className="flex items-center space-x-4 ml-auto pr-0">
          {/* Credits display */}
          <Link 
            href="/settings" 
            className="flex items-center space-x-2 px-3 py-1.5 bg-gray-50 hover:bg-gray-100 rounded-full transition-colors"
          >
            <Coins size={18} className="text-amber-500" />
            {loading ? (
              <span className="w-5 h-5 bg-gray-200 rounded-full animate-pulse"></span>
            ) : (
              <span className="font-medium">{credits}</span>
            )}
            <span className="text-sm text-gray-500 hidden sm:inline">Credits</span>
          </Link>
          
          <UserButton afterSignOutUrl="/" />
        </div>
      </div>
    </header>
  );
}